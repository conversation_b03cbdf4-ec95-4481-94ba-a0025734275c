import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'
import { UserProfile } from '@/lib/supabase'

interface AuthContextType {
  user: User | null
  profile: UserProfile | null
  session: Session | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ error: any }>
  signOut: () => Promise<void>
  refreshProfile: () => Promise<void>
  clearCache: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  console.log('🔄 AuthProvider component mounting...')
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)

  const fetchUserProfile = useCallback(async (userId: string) => {
    console.log('🔍 fetchUserProfile called with userId:', userId)
    try {
      console.log('📡 Making simple Supabase query for user profile...')

      const { data, error } = await supabase
        .from('user_profiles')
        .select('id, user_id, role, company_id, created_at, updated_at')
        .eq('user_id', userId)
        .single()

      if (error) {
        console.error('❌ Error fetching user profile:', error)
        return null
      }

      console.log('✅ User profile fetched successfully:', data)
      return data
    } catch (error) {
      console.error('❌ Unexpected error fetching user profile:', error)
      return null
    }
  }, [])

  const refreshProfile = useCallback(async () => {
    if (user) {
      const userProfile = await fetchUserProfile(user.id)
      setProfile(userProfile)
    }
  }, [user, fetchUserProfile])

  const clearCache = useCallback(() => {
    console.log('🧹 Clearing all cache and state...')
    setUser(null)
    setProfile(null)
    setSession(null)
    setLoading(false)

    // Clear browser storage
    localStorage.clear()
    sessionStorage.clear()

    // Clear any Supabase cache
    if (typeof window !== 'undefined') {
      // Clear any cached data in IndexedDB or other storage
      window.location.reload()
    }
  }, [])

  useEffect(() => {
    console.log('🚀 AuthContext useEffect running...')

    let isInitialized = false
    let currentUserId: string | null = null
    let isMounted = true

    // Fallback timeout to ensure loading is set to false
    const timeoutId = setTimeout(() => {
      if (isMounted) {
        console.log('⏰ Timeout reached (5s), forcing loading to false')
        setLoading(false)
      }
    }, 5000)

    const handleAuthStateChange = async (event: string, session: any) => {
      if (!isMounted) return

      console.log('🔄 Auth state changed:', event)
      console.log('🔄 Session details:', {
        hasSession: !!session,
        hasUser: !!session?.user,
        userId: session?.user?.id,
        userEmail: session?.user?.email
      })
      clearTimeout(timeoutId)

      if (isMounted) {
        setSession(session)
        setUser(session?.user ?? null)
      }

      // Only fetch profile if user changed or this is the first initialization
      const newUserId = session?.user?.id || null
      const shouldFetchProfile = newUserId !== currentUserId || !isInitialized

      if (session?.user && shouldFetchProfile && isMounted) {
        console.log('👤 User found, fetching profile for user:', session.user.id)
        currentUserId = newUserId
        try {
          const userProfile = await fetchUserProfile(session.user.id)
          console.log('✅ Profile fetch completed:', userProfile)
          if (isMounted) {
            setProfile(userProfile)
          }
        } catch (error) {
          console.error('❌ Error in profile fetch try-catch:', error)
          if (isMounted) {
            setProfile(null)
          }
        }
      } else if (!session?.user) {
        console.log('❌ No user session, clearing profile')
        currentUserId = null
        if (isMounted) {
          setProfile(null)
        }
      } else {
        console.log('👤 Same user, skipping profile fetch')
      }

      console.log('🏁 Setting loading to false')
      if (isMounted) {
        setLoading(false)
      }
      isInitialized = true
    }

    // Set up auth state change listener first
    console.log('🎧 Setting up auth state change listener...')
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(handleAuthStateChange)

    // Get initial session
    console.log('🔍 Checking for initial session...')
    supabase.auth.getSession().then(({ data: { session }, error }) => {
      console.log('🔍 Initial session check result:', {
        hasSession: !!session,
        hasUser: !!session?.user,
        userId: session?.user?.id,
        userEmail: session?.user?.email,
        error: error?.message
      })

      if (error) {
        console.error('❌ Error getting initial session:', error)
        clearTimeout(timeoutId)
        setLoading(false)
        return
      }

      // Handle initial session manually to avoid duplicate calls
      if (!isInitialized) {
        handleAuthStateChange('INITIAL_SESSION', session)
      }
    }).catch((error) => {
      console.error('❌ Error in getSession:', error)
      clearTimeout(timeoutId)
      setLoading(false)
    })

    // Cleanup function
    return () => {
      isMounted = false
      clearTimeout(timeoutId)
      subscription.unsubscribe()
    }
  }, [fetchUserProfile])

  const signIn = async (email: string, password: string) => {
    console.log('🔐 Attempting sign in for email:', email)
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) {
      console.error('❌ Sign in failed:', error)
    } else {
      console.log('✅ Sign in successful:', {
        hasUser: !!data.user,
        hasSession: !!data.session,
        userId: data.user?.id,
        userEmail: data.user?.email
      })
    }

    return { error }
  }

  const signOut = async () => {
    try {
      // Clear local state first
      setUser(null)
      setProfile(null)
      setSession(null)

      // Sign out from Supabase
      const { error } = await supabase.auth.signOut()

      if (error) {
        console.error('Error signing out:', error)
        throw error
      }

      // Clear any cached data
      localStorage.clear()
      sessionStorage.clear()

      // Force reload to ensure clean state
      window.location.href = '/'
    } catch (error) {
      console.error('Sign out failed:', error)
      // Even if there's an error, try to clear local state and redirect
      setUser(null)
      setProfile(null)
      setSession(null)
      window.location.href = '/'
    }
  }

  const value = {
    user,
    profile,
    session,
    loading,
    signIn,
    signOut,
    refreshProfile,
    clearCache,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
