import { useState, useMemo } from "react";
import { Users, Target, Calendar, TrendingUp, UserCheck, UserX, RotateCcw, XCircle, CheckCircle, LogOut, Settings } from "lucide-react";
import { isWithinInterval, parseISO, startOfWeek, endOfWeek, subDays, startOfMonth, endOfMonth, subMonths } from "date-fns";

// Components
import { DashboardHeader } from "@/components/dashboard/DashboardHeader";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { AppointmentsTable } from "@/components/dashboard/AppointmentsTable";
import { AppointmentBreakdown } from "@/components/dashboard/AppointmentBreakdown";
import { TimeSeriesChart } from "@/components/dashboard/TimeSeriesChart";
import { MetricCard } from "@/components/ui/metric-card";
import { Button } from "@/components/ui/button";

// Data and hooks
import { Appointment } from "@/data/mockData";
import { useClientMetrics } from "@/hooks/useClientMetrics";
import { useAuth } from "@/contexts/AuthContext";

const MasterDashboard = () => {
  const { user, profile, signOut, clearCache } = useAuth();
  const { appointments, loading, error } = useClientMetrics();
  const today = new Date();
  const [filters, setFilters] = useState({
    dateRange: {
      // Use a broader date range to include the actual data (last 6 months)
      start: startOfMonth(subMonths(today, 6)),
      end: endOfMonth(today)
    },
    selectedClosers: [] as string[],
    selectedSetters: [] as string[]
  });

  const [visibleMetrics, setVisibleMetrics] = useState({
    totalAppointments: true,
    totalSits: true,
    totalCloses: true,
    noShows: true,
    rescheduled: true,
    notInterested: true,
    disqualified: true,
    appointmentBreakdown: true,
    detailedTable: true,
    performanceChart: true
  });

  const toggleMetric = (metric: string) => {
    setVisibleMetrics(prev => ({
      ...prev,
      [metric]: !prev[metric]
    }));
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  // Filter appointments based on date range and selected closers/setters
  const filteredAppointments = useMemo(() => {
    if (appointments.length === 0) {
      return [];
    }

    const filtered = appointments.filter(appointment => {
      try {
        // Skip appointments without valid dates
        if (!appointment.booked_for || appointment.booked_for === '') {
          return false;
        }

        const appointmentDate = parseISO(appointment.booked_for);
        const isValidDate = !isNaN(appointmentDate.getTime());

        if (!isValidDate) {
          console.warn('Invalid date found:', appointment.booked_for, 'for appointment:', appointment.name);
          return false;
        }

        const isInDateRange = isWithinInterval(appointmentDate, {
          start: filters.dateRange.start,
          end: filters.dateRange.end
        });

        const isCloserMatch = filters.selectedClosers.length === 0 ||
          filters.selectedClosers.includes(appointment.closer_name);

        const isSetterMatch = filters.selectedSetters.length === 0 ||
          filters.selectedSetters.includes(appointment.setter_name);

        return isInDateRange && isCloserMatch && isSetterMatch;
      } catch (error) {
        console.error('Error filtering appointment:', appointment.name, error);
        return false;
      }
    });

    // Log summary for debugging
    if (appointments.length > 0) {
      console.log('Appointment filtering summary:', {
        total: appointments.length,
        filtered: filtered.length,
        dateRange: `${filters.dateRange.start.toLocaleDateString()} - ${filters.dateRange.end.toLocaleDateString()}`
      });
    }

    return filtered;
  }, [appointments, filters]);

  // Calculate metrics for the filtered appointments
  const metrics = useMemo(() => {
    const total = filteredAppointments.length;
    const sits = filteredAppointments.filter(apt => apt.confirmation_disposition?.toLowerCase().includes('sat')).length;
    const closes = filteredAppointments.filter(apt => apt.confirmation_disposition?.toLowerCase().includes('closed')).length;
    const noShows = filteredAppointments.filter(apt => apt.confirmation_disposition?.toLowerCase().includes('no show')).length;
    const rescheduled = filteredAppointments.filter(apt => apt.confirmation_disposition?.toLowerCase().includes('rescheduled')).length;
    const notInterested = filteredAppointments.filter(apt => apt.confirmation_disposition?.toLowerCase().includes('not interested')).length;
    const disqualified = filteredAppointments.filter(apt => apt.confirmation_disposition?.toLowerCase().includes('disqualified')).length;

    // Calculate previous period for comparison
    const daysDiff = Math.ceil((filters.dateRange.end.getTime() - filters.dateRange.start.getTime()) / (1000 * 60 * 60 * 24));
    const previousStart = subDays(filters.dateRange.start, daysDiff);
    const previousEnd = subDays(filters.dateRange.end, daysDiff);
    
    const previousAppointments = appointments.filter(appointment => {
      const appointmentDate = parseISO(appointment.booked_for);
      return isWithinInterval(appointmentDate, {
        start: previousStart,
        end: previousEnd
      });
    });

    const previousTotal = previousAppointments.length;
    const percentageChangeNumeric = previousTotal > 0 ? ((total - previousTotal) / previousTotal) * 100 : 0;
    const percentageChange = percentageChangeNumeric.toFixed(1);

    return {
      total,
      sits,
      closes,
      noShows,
      rescheduled,
      notInterested,
      disqualified,
      noShowsPercentage: total > 0 ? ((noShows / total) * 100).toFixed(1) : '0.0',
      rescheduledPercentage: total > 0 ? ((rescheduled / total) * 100).toFixed(1) : '0.0',
      notInterestedPercentage: total > 0 ? ((notInterested / total) * 100).toFixed(1) : '0.0',
      disqualifiedPercentage: total > 0 ? ((disqualified / total) * 100).toFixed(1) : '0.0',
      percentageChange,
      percentageChangeNumeric
    };
  }, [filteredAppointments, appointments, filters.dateRange]);

  // Get unique closers and setters for filter options
  const closers = useMemo(() => {
    return Array.from(new Set(appointments.map(apt => apt.closer_name))).sort();
  }, [appointments]);

  const setters = useMemo(() => {
    return Array.from(new Set(appointments.map(apt => apt.setter_name))).sort();
  }, [appointments]);

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-dashboard-bg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading master dashboard...</p>
          <div className="mt-4">
            <Button
              onClick={clearCache}
              variant="outline"
              size="sm"
              className="text-xs"
            >
              Clear Cache & Reload
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-dashboard-bg flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-foreground mb-2">Error Loading Data</h2>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-dashboard-bg">
      {/* Custom Header for Master Dashboard */}
      <header className="w-full bg-card border-b border-border px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <img
              src="/lovable-uploads/58030552-ef5b-484d-bb0e-66450b796c1d.png"
              alt="Premura Call Center Dashboard"
              className="h-12 w-12 rounded-lg"
              onError={(e) => {
                const fallback = document.createElement('div');
                fallback.className = 'h-12 w-12 bg-gradient-primary rounded-lg flex items-center justify-center';
                fallback.innerHTML = '<svg class="h-8 w-8 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/></svg>';
                (e.target as HTMLImageElement).parentNode?.replaceChild(fallback, e.target as HTMLImageElement);
              }}
            />
            <div>
              <h1 className="text-xl font-bold text-foreground">Master Dashboard - All Clients</h1>
              <p className="text-sm text-muted-foreground">Complete Analytics Overview</p>
            </div>
          </div>

          <div className="flex items-center">
            <p className="text-lg font-bold text-primary">Super Admin View</p>
          </div>

          <div className="flex items-center space-x-4">
            {user && (
              <div className="flex items-center space-x-3">
                <div className="text-right">
                  <p className="text-sm font-medium text-foreground">{user.email}</p>
                  {profile && (
                    <div className="flex items-center space-x-1">
                      <p className="text-xs text-muted-foreground">
                        {profile.role.replace('_', ' ').toUpperCase()}
                      </p>
                    </div>
                  )}
                </div>

                <Button variant="outline" size="sm" onClick={() => window.location.href = '/admin'}>
                  <Settings className="h-4 w-4 mr-2" />
                  Admin Panel
                </Button>

                <Button variant="outline" size="sm" onClick={handleSignOut}>
                  <LogOut className="h-4 w-4 mr-2" />
                  Sign Out
                </Button>
              </div>
            )}
          </div>
        </div>
      </header>
      
      <main className="container mx-auto px-6 py-6">
        {/* Filters */}
        <FilterBar
          closers={closers}
          setters={setters}
          onFiltersChange={setFilters}
        />

        {/* Performance Over Time Chart */}
        {visibleMetrics.performanceChart && (
          <TimeSeriesChart appointments={filteredAppointments} dateRange={filters.dateRange} />
        )}

        {/* KPI Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {visibleMetrics.totalAppointments && (
            <MetricCard
              title="Total Appointments"
              value={metrics.total}
              subtitle={
                <span>
                  All appointments in selected period<br />
                  <span className="text-blue-600 font-semibold">
                    Change: {metrics.percentageChangeNumeric > 0 ? '+' : ''}{metrics.percentageChange}% vs previous period
                  </span>
                </span>
              }
              icon={Calendar}
              variant="info"
            />
          )}
          {visibleMetrics.totalSits && (
            <MetricCard
              title="Total Sits"
              value={metrics.sits}
              subtitle={
                <span>
                  Successfully sat appointments<br />
                  <span className="text-green-600 font-semibold">
                    Show Rate: {metrics.total > 0 ? ((metrics.sits / metrics.total) * 100).toFixed(1) : '0.0'}%
                  </span>
                </span>
              }
              icon={UserCheck}
              variant="success"
            />
          )}
          {visibleMetrics.totalCloses && (
            <MetricCard
              title="Total Closes"
              value={metrics.closes}
              subtitle={
                <span>
                  Successfully closed deals<br />
                  <span className="text-green-600 font-semibold">
                    Close Rate: {metrics.total > 0 ? ((metrics.closes / metrics.total) * 100).toFixed(1) : '0.0'}%
                  </span>
                </span>
              }
              icon={CheckCircle}
              variant="success"
            />
          )}
        </div>

        {/* Secondary Metrics */}
        {(visibleMetrics.noShows || visibleMetrics.rescheduled || visibleMetrics.notInterested || visibleMetrics.disqualified) && (
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-foreground mb-4">Additional Metrics</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {visibleMetrics.noShows && (
                <MetricCard
                  title="No Shows"
                  value={metrics.noShows}
                  subtitle={`${metrics.noShowsPercentage}% of total appointments`}
                  icon={UserX}
                  variant="destructive"
                />
              )}
              {visibleMetrics.rescheduled && (
                <MetricCard
                  title="Rescheduled"
                  value={metrics.rescheduled}
                  subtitle={`${metrics.rescheduledPercentage}% of total appointments`}
                  icon={RotateCcw}
                  variant="warning"
                />
              )}
              {visibleMetrics.notInterested && (
                <MetricCard
                  title="Not Interested"
                  value={metrics.notInterested}
                  subtitle={`${metrics.notInterestedPercentage}% of total appointments`}
                  icon={UserX}
                  variant="default"
                />
              )}
              {visibleMetrics.disqualified && (
                <MetricCard
                  title="Disqualified"
                  value={metrics.disqualified}
                  subtitle={`${metrics.disqualifiedPercentage}% of total appointments`}
                  icon={Target}
                  variant="default"
                />
              )}
            </div>
          </div>
        )}

        {/* Appointment Breakdown Section */}
        {visibleMetrics.appointmentBreakdown && (
          <AppointmentBreakdown appointments={filteredAppointments} />
        )}

        {/* Detailed Appointments Table */}
        {visibleMetrics.detailedTable && (
          <AppointmentsTable appointments={filteredAppointments} />
        )}
      </main>
    </div>
  );
};

export default MasterDashboard;
