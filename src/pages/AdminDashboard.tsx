import React, { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { supabase, Company, UserProfile } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Plus, Users, Building2, LogOut, BarChart3 } from 'lucide-react'
import { CompanyForm } from '@/components/admin/CompanyForm'
import { UserForm } from '@/components/admin/UserForm'
import { CompanyList } from '@/components/admin/CompanyList'
import { UserList } from '@/components/admin/UserList'

import { toast } from 'sonner'
import { useNavigate } from 'react-router-dom'

export const AdminDashboard: React.FC = () => {
  const { user, profile, signOut } = useAuth()
  const navigate = useNavigate()
  const [companies, setCompanies] = useState<Company[]>([])
  const [users, setUsers] = useState<UserProfile[]>([])
  const [loading, setLoading] = useState(true)
  const [showCompanyForm, setShowCompanyForm] = useState(false)
  const [showUserForm, setShowUserForm] = useState(false)

  const fetchCompanies = async () => {
    try {
      const { data, error } = await supabase
        .from('companies')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      setCompanies(data || [])
    } catch (error) {
      console.error('Error fetching companies:', error)
      toast.error('Failed to fetch companies')
    }
  }

  const fetchUsers = async () => {
    try {
      console.log('🔍 AdminDashboard: Fetching users (simplified)...')

      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        console.error('❌ Error fetching user profiles:', error)
        throw error
      }

      console.log('✅ User profiles fetched:', data?.length || 0, data)
      setUsers(data || [])
    } catch (error) {
      console.error('❌ Error fetching users:', error)
      toast.error('Failed to fetch users')
    }
  }

  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      // Small delay to prevent conflicts with AuthContext profile fetch
      await new Promise(resolve => setTimeout(resolve, 100))
      await Promise.all([fetchCompanies(), fetchUsers()])
      setLoading(false)
    }

    loadData()
  }, [])

  const handleCompanyCreated = () => {
    setShowCompanyForm(false)
    fetchCompanies()
    toast.success('Company created successfully')
  }

  const handleUserCreated = () => {
    setShowUserForm(false)
    fetchUsers()
    toast.success('User created successfully')
  }

  const handleSignOut = async () => {
    await signOut()
  }

  const handleMasterDashboard = () => {
    navigate('/master-dashboard')
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-dashboard-bg">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading admin dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-dashboard-bg">
      {/* Header */}
      <header className="bg-card border-b border-border">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-foreground">Admin Dashboard</h1>
              <p className="text-muted-foreground">
                Welcome back, {user?.email} ({profile?.role})
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <Button onClick={handleMasterDashboard} variant="default">
                <BarChart3 className="mr-2 h-4 w-4" />
                Master Dashboard
              </Button>
              <Button onClick={handleSignOut} variant="outline">
                <LogOut className="mr-2 h-4 w-4" />
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-6 py-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Companies</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{companies.length}</div>
              <p className="text-xs text-muted-foreground">
                Active client companies
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{users.length}</div>
              <p className="text-xs text-muted-foreground">
                Registered users
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Management Tabs */}
        <Tabs defaultValue="companies" className="space-y-6">
          <TabsList>
            <TabsTrigger value="companies">Companies</TabsTrigger>
            <TabsTrigger value="users">Users</TabsTrigger>
          </TabsList>

          <TabsContent value="companies" className="space-y-6">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-xl font-semibold">Company Management</h2>
                <p className="text-muted-foreground">Create and manage client companies</p>
              </div>
              <Button onClick={() => setShowCompanyForm(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Company
              </Button>
            </div>

            {showCompanyForm && (
              <CompanyForm
                onSuccess={handleCompanyCreated}
                onCancel={() => setShowCompanyForm(false)}
              />
            )}

            <CompanyList companies={companies} onUpdate={fetchCompanies} />
          </TabsContent>

          <TabsContent value="users" className="space-y-6">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-xl font-semibold">User Management</h2>
                <p className="text-muted-foreground">Create and manage user accounts</p>
              </div>
              <Button onClick={() => setShowUserForm(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add User
              </Button>
            </div>

            {showUserForm && (
              <UserForm
                companies={companies}
                onSuccess={handleUserCreated}
                onCancel={() => setShowUserForm(false)}
              />
            )}

            <UserList users={users} onUpdate={fetchUsers} />
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}
