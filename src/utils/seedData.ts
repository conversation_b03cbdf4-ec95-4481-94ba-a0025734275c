import { supabase } from '@/lib/supabase'

// Sample data for testing
const sampleClientRawData = [
  {
    "Lead Name": "<PERSON>",
    "Closer's Name": "<PERSON>",
    "Booked For": "5/20/2025 17:00:00",
    "Disposition": "Sat, Closed",
    "Notes": "Great customer, very interested",
    "Phone Number": "************",
    "Address": "123 Main St, Anytown, ST 12345",
    "Setter Name": "<PERSON>",
    "Setter Number": "************",
    "Email": "<EMAIL>",
    "Disposition date": "5/21/2025",
    "Site Survey": "Completed",
    "M1 Commision": 250,
    "M2 Commision": 150,
    "Contact Link": "https://crm.example.com/contact/1",
    "Recording": "https://recordings.example.com/1",
    "Credit Score": 750,
    "Roof Type": "Shingles",
    "Existing Solar": "No",
    "Shading": "No",
    "Appointment Type": "In-Person",
    "Confirmed": "Yes",
    "Contact ID": "CONTACT_000001",
    "Client": "Test Client A",
    "Client ID": "CLIENT_001"
  },
  {
    "Lead Name": "<PERSON> Doe",
    "Closer's Name": "Mike Chen",
    "Booked For": "5/22/2025 14:00:00",
    "Disposition": "Sat",
    "Notes": "Needs to discuss with spouse",
    "Phone Number": "************",
    "Address": "456 Oak Ave, Somewhere, ST 67890",
    "Setter Name": "Lisa Brown",
    "Setter Number": "************",
    "Email": "<EMAIL>",
    "Disposition date": "5/22/2025",
    "Site Survey": "Pending",
    "M1 Commision": 200,
    "M2 Commision": 100,
    "Contact Link": "https://crm.example.com/contact/2",
    "Recording": "https://recordings.example.com/2",
    "Credit Score": 680,
    "Roof Type": "Meadows",
    "Existing Solar": "No",
    "Shading": "Yes",
    "Appointment Type": "Virtual",
    "Confirmed": "Yes",
    "Contact ID": "CONTACT_000002",
    "Client": "Test Client B",
    "Client ID": "CLIENT_002"
  },
  {
    "Lead Name": "Bob Wilson",
    "Closer's Name": "David Rodriguez",
    "Booked For": "5/23/2025 10:00:00",
    "Disposition": "No Show",
    "Notes": "Did not answer calls",
    "Phone Number": "************",
    "Address": "789 Pine St, Elsewhere, ST 13579",
    "Setter Name": "Tony Garcia",
    "Setter Number": "************",
    "Email": "<EMAIL>",
    "Disposition date": "5/23/2025",
    "Site Survey": "Not Completed",
    "M1 Commision": 0,
    "M2 Commision": 0,
    "Contact Link": "https://crm.example.com/contact/3",
    "Recording": "",
    "Credit Score": 620,
    "Roof Type": "Flat Roof",
    "Existing Solar": "Yes",
    "Shading": "No",
    "Appointment Type": "In-Person",
    "Confirmed": "No",
    "Contact ID": "CONTACT_000003",
    "Client": "Test Client A",
    "Client ID": "CLIENT_001"
  },
  {
    "Lead Name": "Alice Johnson",
    "Closer's Name": "Emma Williams",
    "Booked For": "5/24/2025 16:00:00",
    "Disposition": "Rescheduled",
    "Notes": "Requested different time",
    "Phone Number": "************",
    "Address": "321 Elm Dr, Nowhere, ST 24680",
    "Setter Name": "Maria Lopez",
    "Setter Number": "************",
    "Email": "<EMAIL>",
    "Disposition date": "5/24/2025",
    "Site Survey": "Scheduled",
    "M1 Commision": 0,
    "M2 Commision": 0,
    "Contact Link": "https://crm.example.com/contact/4",
    "Recording": "https://recordings.example.com/4",
    "Credit Score": 720,
    "Roof Type": "Shingles",
    "Existing Solar": "No",
    "Shading": "No",
    "Appointment Type": "Virtual",
    "Confirmed": "Yes",
    "Contact ID": "CONTACT_000004",
    "Client": "Test Client C",
    "Client ID": "CLIENT_003"
  },
  {
    "Lead Name": "Charlie Brown",
    "Closer's Name": "James Thompson",
    "Booked For": "5/25/2025 13:00:00",
    "Disposition": "Closed",
    "Notes": "Signed contract immediately",
    "Phone Number": "************",
    "Address": "654 Maple Ln, Anywhere, ST 97531",
    "Setter Name": "Ryan Davis",
    "Setter Number": "************",
    "Email": "<EMAIL>",
    "Disposition date": "5/25/2025",
    "Site Survey": "Completed",
    "M1 Commision": 300,
    "M2 Commision": 200,
    "Contact Link": "https://crm.example.com/contact/5",
    "Recording": "https://recordings.example.com/5",
    "Credit Score": 800,
    "Roof Type": "Meadows",
    "Existing Solar": "No",
    "Shading": "No",
    "Appointment Type": "In-Person",
    "Confirmed": "Yes",
    "Contact ID": "CONTACT_000005",
    "Client": "Test Client A",
    "Client ID": "CLIENT_001"
  }
]

export const seedTestData = async () => {
  try {
    console.log('Starting data seeding...')
    
    // Check if data already exists
    const { count } = await supabase
      .from('client_raw_data')
      .select('*', { count: 'exact', head: true })
    
    if (count && count > 0) {
      console.log(`Data already exists (${count} records). Skipping seeding.`)
      return { success: true, message: `${count} records already exist` }
    }
    
    // Insert sample data
    const { data, error } = await supabase
      .from('client_raw_data')
      .insert(sampleClientRawData)
      .select()
    
    if (error) {
      console.error('Error seeding data:', error)
      return { success: false, error: error.message }
    }
    
    console.log('Data seeded successfully:', data?.length, 'records')
    return { success: true, message: `${data?.length} records inserted` }
    
  } catch (err: any) {
    console.error('Seeding failed:', err)
    return { success: false, error: err.message }
  }
}

export const clearTestData = async () => {
  try {
    const { error } = await supabase
      .from('client_raw_data')
      .delete()
      .neq('id', 'impossible-id') // This will delete all records
    
    if (error) {
      console.error('Error clearing data:', error)
      return { success: false, error: error.message }
    }
    
    console.log('Test data cleared successfully')
    return { success: true, message: 'All test data cleared' }
    
  } catch (err: any) {
    console.error('Clearing failed:', err)
    return { success: false, error: err.message }
  }
}
