import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

console.log('🔧 Supabase configuration:', {
  hasUrl: !!supabaseUrl,
  hasKey: !!supabaseAnonKey,
  url: supabaseUrl ? `${supabaseUrl.substring(0, 30)}...` : 'undefined',
  keyPrefix: supabaseAnonKey ? `${supabaseAnonKey.substring(0, 20)}...` : 'undefined'
})

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables:', {
    VITE_SUPABASE_URL: !!supabaseUrl,
    VITE_SUPABASE_ANON_KEY: !!supabaseAnonKey
  })
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnon<PERSON><PERSON>, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

console.log('✅ Supabase client created successfully')

// Database types
export interface Database {
  public: {
    Tables: {
      companies: {
        Row: {
          id: string
          name: string
          company_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          company_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          company_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      user_profiles: {
        Row: {
          id: string
          user_id: string
          company_id: string | null
          role: 'super_admin' | 'admin' | 'user'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          company_id?: string | null
          role?: 'super_admin' | 'admin' | 'user'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          company_id?: string | null
          role?: 'super_admin' | 'admin' | 'user'
          created_at?: string
          updated_at?: string
        }
      }
      client_metrics_raw: {
        Row: {
          id: string
          client_name: string | null
          company_id: string | null
          name: string
          closer_name: string
          booked_for: string
          confirmation_disposition: string
          note: string
          phone_number: string
          address: string
          setter_name: string
          setter_number: string
          email: string
          disposition_date: string
          site_survey: string
          m1_commission: number
          m2_commission: number
          contact_link: string
          recording_media_link: string
          credit_score: number
          roof_type: string
          existing_solar: boolean
          shading: string
          appointment_type: string
          confirmed: boolean
          contact_ID: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          client_name?: string | null
          company_id?: string | null
          name: string
          closer_name: string
          booked_for: string
          confirmation_disposition: string
          note: string
          phone_number: string
          address: string
          setter_name: string
          setter_number: string
          email: string
          disposition_date: string
          site_survey: string
          m1_commission: number
          m2_commission: number
          contact_link: string
          recording_media_link: string
          credit_score: number
          roof_type: string
          existing_solar: boolean
          shading: string
          appointment_type: string
          confirmed: boolean
          contact_ID: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          client_name?: string | null
          company_id?: string | null
          name?: string
          closer_name?: string
          booked_for?: string
          confirmation_disposition?: string
          note?: string
          phone_number?: string
          address?: string
          setter_name?: string
          setter_number?: string
          email?: string
          disposition_date?: string
          site_survey?: string
          m1_commission?: number
          m2_commission?: number
          contact_link?: string
          recording_media_link?: string
          credit_score?: number
          roof_type?: string
          existing_solar?: boolean
          shading?: string
          appointment_type?: string
          confirmed?: boolean
          contact_ID?: string
          created_at?: string
          updated_at?: string
        }
      }
      client_raw_data: {
        Row: {
          "Date": string | null
          "Lead Name": string | null
          "Closer's Name": string | null
          "Client": string | null
          "Booked For": string | null
          "Confirmation": string | null
          "Disposition": string | null
          "Notes": string | null
          "Phone Number": number | null
          "Address": string | null
          "Setter Name": string | null
          "Setter Number": string | null
          "Disposition date": string | null
          "Email": string | null
          "Site Survey": string | null
          "M1 Commision": string | null
          "M2 Commision": string | null
          "Contact Link": string | null
          "Recording": string | null
          "Media Link": string | null
          "DQ Reason": string | null
          "test": string | null
          "Credit Score": string | null
          "Roof Type": string | null
          "Existing Solar": string | null
          "Shading": string | null
          "Appointment Type": string | null
          "Never Booked (Manual)": string | null
          "Confirmed": string | null
          "Cancelled": string | null
          "No hold": string | null
          "Follow UP": string | null
          "Rescheduled": string | null
          "DQ": string | null
          "Sat": string | null
          "Closed": string | null
          "Contact ID": string | null
          "Repeated": string | null
          "Repeated down": string | null
          "Client ID": string | null
        }
        Insert: {
          id?: string
          client_name?: string | null
          company_id?: string | null
          name: string
          closer_name: string
          booked_for: string
          confirmation_disposition: string
          note: string
          phone_number: string
          address: string
          setter_name: string
          setter_number: string
          email: string
          disposition_date: string
          site_survey: string
          m1_commission: number
          m2_commission: number
          contact_link: string
          recording_media_link: string
          credit_score: number
          roof_type: string
          existing_solar: boolean
          shading: string
          appointment_type: string
          confirmed: boolean
          contact_ID: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          client_name?: string | null
          company_id?: string | null
          name?: string
          closer_name?: string
          booked_for?: string
          confirmation_disposition?: string
          note?: string
          phone_number?: string
          address?: string
          setter_name?: string
          setter_number?: string
          email?: string
          disposition_date?: string
          site_survey?: string
          m1_commission?: number
          m2_commission?: number
          contact_link?: string
          recording_media_link?: string
          credit_score?: number
          roof_type?: string
          existing_solar?: boolean
          shading?: string
          appointment_type?: string
          confirmed?: boolean
          contact_ID?: string
          created_at?: string
          updated_at?: string
        }
      }
      client_raw_data: {
        Row: {
          "Date": string | null
          "Lead Name": string | null
          "Closer's Name": string | null
          "Client": string | null
          "Booked For": string | null
          "Confirmation": string | null
          "Disposition": string | null
          "Notes": string | null
          "Phone Number": number | null
          "Address": string | null
          "Setter Name": string | null
          "Setter Number": string | null
          "Disposition date": string | null
          "Email": string | null
          "Site Survey": string | null
          "M1 Commision": string | null
          "M2 Commision": string | null
          "Contact Link": string | null
          "Recording": string | null
          "Media Link": string | null
          "DQ Reason": string | null
          "test": string | null
          "Credit Score": string | null
          "Roof Type": string | null
          "Existing Solar": string | null
          "Shading": string | null
          "Appointment Type": string | null
          "Never Booked (Manual)": string | null
          "Confirmed": string | null
          "Cancelled": string | null
          "No hold": string | null
          "Follow UP": string | null
          "Rescheduled": string | null
          "DQ": string | null
          "Sat": string | null
          "Closed": string | null
          "Contact ID": string | null
          "Repeated": string | null
          "Repeated down": string | null
          "Client ID": string | null
        }
        Insert: {
          "Date"?: string | null
          "Lead Name"?: string | null
          "Closer's Name"?: string | null
          "Client"?: string | null
          "Booked For"?: string | null
          "Confirmation"?: string | null
          "Disposition"?: string | null
          "Notes"?: string | null
          "Phone Number"?: number | null
          "Address"?: string | null
          "Setter Name"?: string | null
          "Setter Number"?: string | null
          "Disposition date"?: string | null
          "Email"?: string | null
          "Site Survey"?: string | null
          "M1 Commision"?: string | null
          "M2 Commision"?: string | null
          "Contact Link"?: string | null
          "Recording"?: string | null
          "Media Link"?: string | null
          "DQ Reason"?: string | null
          "test"?: string | null
          "Credit Score"?: string | null
          "Roof Type"?: string | null
          "Existing Solar"?: string | null
          "Shading"?: string | null
          "Appointment Type"?: string | null
          "Never Booked (Manual)"?: string | null
          "Confirmed"?: string | null
          "Cancelled"?: string | null
          "No hold"?: string | null
          "Follow UP"?: string | null
          "Rescheduled"?: string | null
          "DQ"?: string | null
          "Sat"?: string | null
          "Closed"?: string | null
          "Contact ID"?: string | null
          "Repeated"?: string | null
          "Repeated down"?: string | null
          "Client ID"?: string | null
        }
        Update: {
          "Date"?: string | null
          "Lead Name"?: string | null
          "Closer's Name"?: string | null
          "Client"?: string | null
          "Booked For"?: string | null
          "Confirmation"?: string | null
          "Disposition"?: string | null
          "Notes"?: string | null
          "Phone Number"?: number | null
          "Address"?: string | null
          "Setter Name"?: string | null
          "Setter Number"?: string | null
          "Disposition date"?: string | null
          "Email"?: string | null
          "Site Survey"?: string | null
          "M1 Commision"?: string | null
          "M2 Commision"?: string | null
          "Contact Link"?: string | null
          "Recording"?: string | null
          "Media Link"?: string | null
          "DQ Reason"?: string | null
          "test"?: string | null
          "Credit Score"?: string | null
          "Roof Type"?: string | null
          "Existing Solar"?: string | null
          "Shading"?: string | null
          "Appointment Type"?: string | null
          "Never Booked (Manual)"?: string | null
          "Confirmed"?: string | null
          "Cancelled"?: string | null
          "No hold"?: string | null
          "Follow UP"?: string | null
          "Rescheduled"?: string | null
          "DQ"?: string | null
          "Sat"?: string | null
          "Closed"?: string | null
          "Contact ID"?: string | null
          "Repeated"?: string | null
          "Repeated down"?: string | null
          "Client ID"?: string | null
        }
      }
    }
  }
}

export type Company = Database['public']['Tables']['companies']['Row']
export type UserProfile = Database['public']['Tables']['user_profiles']['Row']
export type ClientMetric = Database['public']['Tables']['client_metrics_raw']['Row']
export type ClientRawData = Database['public']['Tables']['client_raw_data']['Row']

// Helper function to map database columns to our interface
export const mapClientMetricToAppointment = (metric: any) => {
  return {
    name: metric.name || '',
    closer_name: metric.closer_name || '',
    booked_for: metric.booked_for || '',
    confirmation_disposition: metric.confirmation_disposition || 'Pending',
    note: metric.note || '',
    phone_number: metric.phone_number?.toString() || '',
    address: metric.address || '',
    setter_name: metric.setter_name || '',
    setter_number: metric.setter_number || '',
    email: metric.email || '',
    disposition_date: metric.disposition_date || '',
    site_survey: metric.site_survey || '',
    m1_commission: parseFloat(metric.m1_commission) || 0,
    m2_commission: parseFloat(metric.m2_commission) || 0,
    contact_link: metric.contact_link || '',
    recording_media_link: metric.recording_media_link || '',
    credit_score: parseInt(metric.credit_score) || 0,
    roof_type: metric.roof_type || '',
    existing_solar: metric.existing_solar === true,
    shading: metric.shading || '',
    appointment_type: metric.appointment_type || '',
    confirmed: metric.confirmed === true,
    contact_ID: metric.contact_id || '',
    client_name: metric.client_name || '',
    company_id: metric.company_id || ''
  }
}

// Helper function to map client_raw_data columns to our interface
export const mapClientRawDataToAppointment = (rawData: any) => {
  // Helper function to safely parse numbers
  const safeParseFloat = (value: any): number => {
    if (!value || value === '') return 0
    const parsed = parseFloat(value.toString())
    return isNaN(parsed) ? 0 : parsed
  }

  const safeParseInt = (value: any): number => {
    if (!value || value === '') return 0
    const parsed = parseInt(value.toString())
    return isNaN(parsed) ? 0 : parsed
  }

  // Helper function to parse dates in M/D/YYYY H:mm:ss format to ISO format
  const parseCustomDate = (dateStr: any): string => {
    if (!dateStr || dateStr === '') return ''

    try {
      const str = dateStr.toString().trim()

      // Check if it's already in ISO format (YYYY-MM-DD)
      if (str.match(/^\d{4}-\d{2}-\d{2}/)) {
        return str
      }

      // Parse M/D/YYYY H:mm:ss format
      const dateMatch = str.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{2}):(\d{2})$/)
      if (dateMatch) {
        const [, month, day, year, hour, minute, second] = dateMatch
        // Convert to ISO format: YYYY-MM-DDTHH:mm:ss
        const isoDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T${hour.padStart(2, '0')}:${minute}:${second}`
        return isoDate
      }

      // Try to parse other common formats
      const date = new Date(str)
      if (!isNaN(date.getTime())) {
        return date.toISOString()
      }

      console.warn('Unable to parse date:', str)
      return ''
    } catch (error) {
      console.warn('Error parsing date:', dateStr, error)
      return ''
    }
  }

  // Determine disposition based on available data
  // Priority order: explicit Disposition field, then check individual status columns
  let disposition = 'Pending'

  if (rawData["Disposition"] && rawData["Disposition"].trim() !== '') {
    // Keep the original compound disposition value so "Sat, Closed" counts for both Sat and Closed
    disposition = rawData["Disposition"].toString().trim()
  } else {
    // Check individual status columns (these seem to contain 'x' or other markers when true)
    if (rawData["Closed"] && rawData["Closed"].toString().trim() !== '') {
      disposition = 'Closed'
    } else if (rawData["Sat"] && rawData["Sat"].toString().trim() !== '') {
      disposition = 'Sat'
    } else if (rawData["Rescheduled"] && rawData["Rescheduled"].toString().trim() !== '') {
      disposition = 'Rescheduled'
    } else if (rawData["DQ"] && rawData["DQ"].toString().trim() !== '') {
      disposition = 'Disqualified'
    } else if (rawData["Follow UP"] && rawData["Follow UP"].toString().trim() !== '') {
      disposition = 'Follow-up'
    } else if (rawData["Cancelled"] && rawData["Cancelled"].toString().trim() !== '') {
      disposition = 'No Show'
    } else if (rawData["Never Booked (Manual)"] && rawData["Never Booked (Manual)"].toString().trim() !== '') {
      disposition = 'Not Interested'
    }
  }

  const mappedData = {
    name: rawData["Lead Name"] || '',
    closer_name: rawData["Closer's Name"] || '',
    booked_for: parseCustomDate(rawData["Booked For"]),
    confirmation_disposition: disposition,
    note: rawData["Notes"] || '',
    phone_number: rawData["Phone Number"]?.toString() || '',
    address: rawData["Address"] || '',
    setter_name: rawData["Setter Name"] || '',
    setter_number: rawData["Setter Number"] || '',
    email: rawData["Email"] || '',
    disposition_date: parseCustomDate(rawData["Disposition date"] || rawData["Date"]),
    site_survey: rawData["Site Survey"] || '',
    m1_commission: safeParseFloat(rawData["M1 Commision"]),
    m2_commission: safeParseFloat(rawData["M2 Commision"]),
    contact_link: rawData["Contact Link"] || '',
    recording_media_link: rawData["Recording"] || rawData["Media Link"] || '',
    credit_score: safeParseInt(rawData["Credit Score"]),
    roof_type: rawData["Roof Type"] || '',
    existing_solar: rawData["Existing Solar"]?.toLowerCase() === 'yes' || rawData["Existing Solar"]?.toLowerCase() === 'true',
    shading: rawData["Shading"] || '',
    appointment_type: rawData["Appointment Type"] || '',
    confirmed: rawData["Confirmed"]?.toLowerCase() === 'yes' || rawData["Confirmed"]?.toLowerCase() === 'true',
    contact_ID: rawData["Contact ID"] || '',
    client_name: rawData["Client"] || '',
    company_id: rawData["Client ID"] || '' // Using Client ID for multi-tenancy
  }



  return mappedData
}
