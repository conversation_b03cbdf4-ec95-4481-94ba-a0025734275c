import { useState, useEffect } from 'react'
import { supabase, mapClientRawDataToAppointment } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'
import { Appointment } from '@/data/mockData'

export const useClientMetrics = () => {
  const [appointments, setAppointments] = useState<Appointment[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { profile } = useAuth()

  const fetchMetrics = async () => {
    try {
      setLoading(true)
      setError(null)

      if (!profile) {
        console.log('No profile found, skipping data fetch')
        setAppointments([])
        setLoading(false)
        return
      }

      console.log('Fetching data for profile:', profile)

      let query = supabase.from('client_raw_data').select('*')

      // Filter by Client ID for non-super admin users
      // The Client ID column in client_raw_data should match the user's company_id
      if (profile.role !== 'super_admin' && profile.company_id) {
        console.log('Filtering by Client ID:', profile.company_id)
        query = query.eq('Client ID', profile.company_id)
      } else if (profile.role === 'super_admin') {
        console.log('Super admin - fetching all data (no filter applied)')
        // For super admin, we don't apply any filter to get all data
      } else {
        console.log('User has no company_id and is not super admin - this might be an issue')
        // If user is not super admin and has no company_id, they shouldn't see any data
        setAppointments([])
        setLoading(false)
        return
      }

      const { data, error: fetchError } = await query

      if (fetchError) {
        console.error('Supabase query error:', fetchError)
        console.error('Query details:', {
          role: profile.role,
          company_id: profile.company_id,
          isSupeAdmin: profile.role === 'super_admin'
        })
        throw fetchError
      }

      console.log('Raw data fetched:', data?.length, 'records')
      console.log('Sample raw data:', data?.[0])

      // Additional logging for debugging
      if (data && data.length === 0) {
        console.warn('No data returned from query. This could indicate:')
        console.warn('1. Empty table')
        console.warn('2. Incorrect filtering')
        console.warn('3. Permission issues')
        console.warn('Query context:', {
          role: profile.role,
          company_id: profile.company_id,
          table: 'client_raw_data'
        })
      }

      // Check how many records have disposition data
      const recordsWithDisposition = data?.filter(record =>
        record["Disposition"] && record["Disposition"].toString().trim() !== ''
      ).length || 0
      console.log('Records with disposition data:', recordsWithDisposition, 'out of', data?.length)

      // Map the database records to our appointment interface
      const mappedAppointments = (data || []).map(mapClientRawDataToAppointment)
      console.log('Successfully loaded', mappedAppointments.length, 'appointments')

      // Check disposition distribution
      const dispositionCounts = mappedAppointments.reduce((acc, apt) => {
        const disp = apt.confirmation_disposition || 'Empty'
        acc[disp] = (acc[disp] || 0) + 1
        return acc
      }, {} as Record<string, number>)
      console.log('Disposition distribution:', dispositionCounts)

      setAppointments(mappedAppointments)
    } catch (err: any) {
      console.error('Error fetching client raw data:', err)
      setError(err.message)
      setAppointments([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchMetrics()
  }, [profile])

  return {
    appointments,
    loading,
    error,
    refetch: fetchMetrics
  }
}
