import React, { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

export const AuthStateDebugger: React.FC = () => {
  // Disable in production
  if (process.env.NODE_ENV === 'production') {
    return null
  }

  const { user, profile, session, loading } = useAuth()
  const [supabaseSession, setSupabaseSession] = useState<any>(null)
  const [logs, setLogs] = useState<string[]>([])

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [...prev.slice(-9), `${timestamp}: ${message}`])
  }

  useEffect(() => {
    addLog('AuthStateDebugger mounted')
    
    // Check Supabase session directly
    supabase.auth.getSession().then(({ data: { session }, error }) => {
      addLog(`Direct Supabase session check: ${session ? 'Found' : 'Not found'}`)
      if (error) addLog(`Session error: ${error.message}`)
      setSupabaseSession(session)
    })

    // Listen to auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      addLog(`Auth state change: ${event}`)
      setSupabaseSession(session)
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  const testLogin = async () => {
    addLog('Testing login...')
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'Simha@2025'
    })
    
    if (error) {
      addLog(`Login failed: ${error.message}`)
    } else {
      addLog('Login successful!')
    }
  }

  const testLogout = async () => {
    addLog('Testing logout...')
    const { error } = await supabase.auth.signOut()
    if (error) {
      addLog(`Logout failed: ${error.message}`)
    } else {
      addLog('Logout successful!')
    }
  }

  return (
    <div className="p-4 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Auth State Debugger</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <Button onClick={testLogin}>Test Login</Button>
            <Button onClick={testLogout} variant="outline">Test Logout</Button>
          </div>
          
          <div>
            <h3 className="font-semibold mb-2">Auth Context State:</h3>
            <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
              {JSON.stringify({
                loading,
                hasUser: !!user,
                hasProfile: !!profile,
                hasSession: !!session,
                userEmail: user?.email,
                userId: user?.id,
                profileRole: profile?.role,
                profileCompanyId: profile?.company_id
              }, null, 2)}
            </pre>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Direct Supabase Session:</h3>
            <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
              {JSON.stringify({
                hasSession: !!supabaseSession,
                hasUser: !!supabaseSession?.user,
                userEmail: supabaseSession?.user?.email,
                userId: supabaseSession?.user?.id,
                expiresAt: supabaseSession?.expires_at
              }, null, 2)}
            </pre>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Recent Logs:</h3>
            <div className="bg-gray-100 p-2 rounded text-xs max-h-40 overflow-auto">
              {logs.map((log, index) => (
                <div key={index}>{log}</div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
