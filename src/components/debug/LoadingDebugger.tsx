import React, { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export const LoadingDebugger: React.FC = () => {
  // Disable in production
  if (process.env.NODE_ENV === 'production') {
    return null
  }

  const { user, profile, loading: authLoading } = useAuth()
  const [testResults, setTestResults] = useState<any>({})
  const [testing, setTesting] = useState(false)

  const runTests = async () => {
    setTesting(true)
    const results: any = {}

    try {
      // Test 1: Check auth state
      results.authState = {
        hasUser: !!user,
        hasProfile: !!profile,
        authLoading,
        userEmail: user?.email,
        profileRole: profile?.role,
        profileCompanyId: profile?.company_id
      }

      // Test 2: Test the old problematic query (should return empty)
      try {
        const { data: oldQuery, error: oldError } = await supabase
          .from('user_profiles')
          .select(`
            *,
            companies (
              name,
              company_id
            )
          `)
          .order('created_at', { ascending: false })

        results.oldQuery = {
          success: !oldError,
          error: oldError?.message,
          dataCount: oldQuery?.length || 0,
          data: oldQuery
        }
      } catch (error: any) {
        results.oldQuery = {
          success: false,
          error: error.message,
          dataCount: 0
        }
      }

      // Test 3: Test the new fixed query (should return data)
      try {
        const { data: newQuery, error: newError } = await supabase
          .from('user_profiles')
          .select(`
            *,
            companies!left (
              name,
              company_id
            )
          `)
          .order('created_at', { ascending: false })

        results.newQuery = {
          success: !newError,
          error: newError?.message,
          dataCount: newQuery?.length || 0,
          data: newQuery
        }
      } catch (error: any) {
        results.newQuery = {
          success: false,
          error: error.message,
          dataCount: 0
        }
      }

      // Test 4: Test client_raw_data access
      try {
        const { data: clientData, error: clientError } = await supabase
          .from('client_raw_data')
          .select('*')
          .limit(5)

        results.clientData = {
          success: !clientError,
          error: clientError?.message,
          dataCount: clientData?.length || 0,
          hasClientIdColumn: clientData?.[0] ? 'Client ID' in clientData[0] : false
        }
      } catch (error: any) {
        results.clientData = {
          success: false,
          error: error.message,
          dataCount: 0
        }
      }

      setTestResults(results)
    } catch (error: any) {
      console.error('Test error:', error)
      setTestResults({ error: error.message })
    } finally {
      setTesting(false)
    }
  }

  useEffect(() => {
    if (!authLoading && user && profile) {
      runTests()
    }
  }, [authLoading, user, profile])

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Loading Debugger</CardTitle>
        <Button onClick={runTests} disabled={testing}>
          {testing ? 'Running Tests...' : 'Run Tests'}
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h3 className="font-semibold">Auth State:</h3>
            <pre className="bg-gray-100 p-2 rounded text-sm overflow-auto">
              {JSON.stringify(testResults.authState, null, 2)}
            </pre>
          </div>

          <div>
            <h3 className="font-semibold">Old Query (INNER JOIN - should fail):</h3>
            <pre className="bg-gray-100 p-2 rounded text-sm overflow-auto">
              {JSON.stringify(testResults.oldQuery, null, 2)}
            </pre>
          </div>

          <div>
            <h3 className="font-semibold">New Query (LEFT JOIN - should work):</h3>
            <pre className="bg-gray-100 p-2 rounded text-sm overflow-auto">
              {JSON.stringify(testResults.newQuery, null, 2)}
            </pre>
          </div>

          <div>
            <h3 className="font-semibold">Client Data Access:</h3>
            <pre className="bg-gray-100 p-2 rounded text-sm overflow-auto">
              {JSON.stringify(testResults.clientData, null, 2)}
            </pre>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
