import React, { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useClientMetrics } from '@/hooks/useClientMetrics'
import { supabase } from '@/lib/supabase'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'

export const KPIDebugger: React.FC = () => {
  // Disable in production
  if (process.env.NODE_ENV === 'production') {
    return null
  }

  const { user, profile } = useAuth()
  const { appointments, loading, error, refetch } = useClientMetrics()
  const [debugData, setDebugData] = useState<any>({})
  const [isDebugging, setIsDebugging] = useState(false)

  const runDiagnostics = async () => {
    setIsDebugging(true)
    const diagnostics: any = {
      timestamp: new Date().toISOString(),
      authStatus: {},
      databaseStatus: {},
      dataFlow: {},
      kpiCalculations: {}
    }

    try {
      // 1. Check authentication status
      diagnostics.authStatus = {
        hasUser: !!user,
        hasProfile: !!profile,
        userEmail: user?.email,
        profileRole: profile?.role,
        profileCompanyId: profile?.company_id,
        isSupeAdmin: profile?.role === 'super_admin'
      }

      // 2. Check database connection and data
      const { data: rawData, error: dbError } = await supabase
        .from('client_raw_data')
        .select('*')
        .limit(5)

      diagnostics.databaseStatus = {
        connectionSuccess: !dbError,
        error: dbError?.message,
        recordCount: rawData?.length || 0,
        sampleRecord: rawData?.[0] || null,
        hasClientIdColumn: rawData?.[0] ? 'Client ID' in rawData[0] : false,
        hasDispositionColumn: rawData?.[0] ? 'Disposition' in rawData[0] : false
      }

      // 3. Check data flow
      diagnostics.dataFlow = {
        appointmentsLoading: loading,
        appointmentsError: error,
        appointmentsCount: appointments.length,
        hookError: error,
        sampleAppointment: appointments[0] || null
      }

      // 4. Calculate KPIs manually to verify logic
      if (appointments.length > 0) {
        const total = appointments.length
        const sits = appointments.filter(apt => 
          apt.confirmation_disposition?.toLowerCase().includes('sat')
        ).length
        const closes = appointments.filter(apt => 
          apt.confirmation_disposition?.toLowerCase().includes('closed')
        ).length
        const noShows = appointments.filter(apt => 
          apt.confirmation_disposition?.toLowerCase().includes('no show')
        ).length

        diagnostics.kpiCalculations = {
          total,
          sits,
          closes,
          noShows,
          dispositionBreakdown: appointments.reduce((acc, apt) => {
            const disp = apt.confirmation_disposition || 'Empty'
            acc[disp] = (acc[disp] || 0) + 1
            return acc
          }, {} as Record<string, number>)
        }
      }

      // 5. Check if super admin should see all data
      if (profile?.role === 'super_admin') {
        const { count } = await supabase
          .from('client_raw_data')
          .select('*', { count: 'exact', head: true })
        
        diagnostics.superAdminCheck = {
          totalRecordsInDB: count,
          shouldSeeAllData: true,
          actualAppointmentCount: appointments.length,
          dataMatchesExpectation: count === appointments.length
        }
      }

      setDebugData(diagnostics)
      console.log('KPI Diagnostics:', diagnostics)

    } catch (err: any) {
      diagnostics.error = err.message
      setDebugData(diagnostics)
    } finally {
      setIsDebugging(false)
    }
  }

  useEffect(() => {
    if (profile) {
      runDiagnostics()
    }
  }, [profile, appointments])

  const getStatusColor = (status: boolean) => status ? 'bg-green-500' : 'bg-red-500'

  return (
    <Card className="w-full max-w-4xl mx-auto mt-4">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          KPI Diagnostics
          <Button onClick={runDiagnostics} disabled={isDebugging} size="sm">
            {isDebugging ? 'Running...' : 'Run Diagnostics'}
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {Object.keys(debugData).length > 0 && (
          <>
            {/* Auth Status */}
            <div>
              <h3 className="font-semibold mb-2">Authentication Status</h3>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex items-center gap-2">
                  <div className={`w-3 h-3 rounded-full ${getStatusColor(debugData.authStatus?.hasUser)}`}></div>
                  User Authenticated: {debugData.authStatus?.hasUser ? 'Yes' : 'No'}
                </div>
                <div className="flex items-center gap-2">
                  <div className={`w-3 h-3 rounded-full ${getStatusColor(debugData.authStatus?.hasProfile)}`}></div>
                  Profile Loaded: {debugData.authStatus?.hasProfile ? 'Yes' : 'No'}
                </div>
                <div>Email: {debugData.authStatus?.userEmail || 'N/A'}</div>
                <div>Role: {debugData.authStatus?.profileRole || 'N/A'}</div>
                <div>Company ID: {debugData.authStatus?.profileCompanyId || 'null'}</div>
                <div>Is Super Admin: {debugData.authStatus?.isSupeAdmin ? 'Yes' : 'No'}</div>
              </div>
            </div>

            {/* Database Status */}
            <div>
              <h3 className="font-semibold mb-2">Database Status</h3>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex items-center gap-2">
                  <div className={`w-3 h-3 rounded-full ${getStatusColor(debugData.databaseStatus?.connectionSuccess)}`}></div>
                  Connection: {debugData.databaseStatus?.connectionSuccess ? 'Success' : 'Failed'}
                </div>
                <div>Records Found: {debugData.databaseStatus?.recordCount || 0}</div>
                <div className="flex items-center gap-2">
                  <div className={`w-3 h-3 rounded-full ${getStatusColor(debugData.databaseStatus?.hasClientIdColumn)}`}></div>
                  Has Client ID Column: {debugData.databaseStatus?.hasClientIdColumn ? 'Yes' : 'No'}
                </div>
                <div className="flex items-center gap-2">
                  <div className={`w-3 h-3 rounded-full ${getStatusColor(debugData.databaseStatus?.hasDispositionColumn)}`}></div>
                  Has Disposition Column: {debugData.databaseStatus?.hasDispositionColumn ? 'Yes' : 'No'}
                </div>
              </div>
              {debugData.databaseStatus?.error && (
                <Alert variant="destructive" className="mt-2">
                  <AlertDescription>DB Error: {debugData.databaseStatus.error}</AlertDescription>
                </Alert>
              )}
            </div>

            {/* Data Flow */}
            <div>
              <h3 className="font-semibold mb-2">Data Flow Status</h3>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>Loading: {debugData.dataFlow?.appointmentsLoading ? 'Yes' : 'No'}</div>
                <div>Appointments Count: {debugData.dataFlow?.appointmentsCount || 0}</div>
                {debugData.dataFlow?.appointmentsError && (
                  <div className="col-span-2">
                    <Alert variant="destructive">
                      <AlertDescription>Hook Error: {debugData.dataFlow.appointmentsError}</AlertDescription>
                    </Alert>
                  </div>
                )}
              </div>
            </div>

            {/* KPI Calculations */}
            {debugData.kpiCalculations && (
              <div>
                <h3 className="font-semibold mb-2">KPI Calculations</h3>
                <div className="grid grid-cols-4 gap-2 text-sm">
                  <div>Total: <Badge>{debugData.kpiCalculations.total}</Badge></div>
                  <div>Sits: <Badge>{debugData.kpiCalculations.sits}</Badge></div>
                  <div>Closes: <Badge>{debugData.kpiCalculations.closes}</Badge></div>
                  <div>No Shows: <Badge>{debugData.kpiCalculations.noShows}</Badge></div>
                </div>
                <div className="mt-2">
                  <h4 className="font-medium">Disposition Breakdown:</h4>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {Object.entries(debugData.kpiCalculations.dispositionBreakdown || {}).map(([disp, count]) => (
                      <Badge key={disp} variant="outline" className="text-xs">
                        {disp}: {count as number}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Super Admin Check */}
            {debugData.superAdminCheck && (
              <div>
                <h3 className="font-semibold mb-2">Super Admin Data Check</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>Total DB Records: {debugData.superAdminCheck.totalRecordsInDB}</div>
                  <div>Mapped Appointments: {debugData.superAdminCheck.actualAppointmentCount}</div>
                  <div className="flex items-center gap-2">
                    <div className={`w-3 h-3 rounded-full ${getStatusColor(debugData.superAdminCheck.dataMatchesExpectation)}`}></div>
                    Data Consistency: {debugData.superAdminCheck.dataMatchesExpectation ? 'Good' : 'Issue'}
                  </div>
                </div>
              </div>
            )}

            {/* Raw Debug Data */}
            <details className="mt-4">
              <summary className="cursor-pointer font-medium">Raw Debug Data</summary>
              <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40 mt-2">
                {JSON.stringify(debugData, null, 2)}
              </pre>
            </details>
          </>
        )}
      </CardContent>
    </Card>
  )
}
