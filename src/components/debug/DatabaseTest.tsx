import React, { useState } from 'react'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export const DatabaseTest: React.FC = () => {
  // Disable in production
  if (process.env.NODE_ENV === 'production') {
    return null
  }

  const [results, setResults] = useState<any>({})
  const [loading, setLoading] = useState(false)

  const testDatabase = async () => {
    setLoading(true)
    const testResults: any = {}

    try {
      // Test 1: Check if we can connect to Supabase
      console.log('Testing Supabase connection...')
      const { data: authUser } = await supabase.auth.getUser()
      testResults.authConnection = {
        success: true,
        user: authUser.user ? { id: authUser.user.id, email: authUser.user.email } : null
      }

      // Test 2: Check client_raw_data table structure
      console.log('Testing client_raw_data table...')
      const { data: tableData, error: tableError } = await supabase
        .from('client_raw_data')
        .select('*')
        .limit(1)
      
      testResults.tableStructure = {
        success: !tableError,
        error: tableError?.message,
        columns: tableData?.[0] ? Object.keys(tableData[0]) : [],
        sampleData: tableData?.[0]
      }

      // Test 3: Count total records
      console.log('Counting total records...')
      const { count, error: countError } = await supabase
        .from('client_raw_data')
        .select('*', { count: 'exact', head: true })
      
      testResults.recordCount = {
        success: !countError,
        error: countError?.message,
        count
      }

      // Test 4: Check user_profiles table
      console.log('Testing user_profiles table...')
      const { data: profileData, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .limit(5)
      
      testResults.userProfiles = {
        success: !profileError,
        error: profileError?.message,
        profiles: profileData
      }

      // Test 5: Check for specific columns we need
      if (tableData?.[0]) {
        const requiredColumns = ['Client ID', 'Disposition', 'Lead Name', 'Booked For']
        const missingColumns = requiredColumns.filter(col => !(col in tableData[0]))
        testResults.columnCheck = {
          requiredColumns,
          missingColumns,
          hasAllRequired: missingColumns.length === 0
        }
      }

      // Test 6: Sample data with dispositions
      console.log('Checking disposition data...')
      const { data: dispositionData, error: dispError } = await supabase
        .from('client_raw_data')
        .select('Disposition, "Lead Name", "Client ID"')
        .not('Disposition', 'is', null)
        .limit(10)
      
      testResults.dispositionSample = {
        success: !dispError,
        error: dispError?.message,
        data: dispositionData
      }

    } catch (error: any) {
      testResults.generalError = error.message
    }

    setResults(testResults)
    setLoading(false)
    console.log('Database test results:', testResults)
  }

  return (
    <Card className="w-full max-w-4xl mx-auto mt-8">
      <CardHeader>
        <CardTitle>Database Connection Test</CardTitle>
        <Button onClick={testDatabase} disabled={loading}>
          {loading ? 'Testing...' : 'Run Database Tests'}
        </Button>
      </CardHeader>
      <CardContent>
        {Object.keys(results).length > 0 && (
          <div className="space-y-4">
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
              {JSON.stringify(results, null, 2)}
            </pre>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
