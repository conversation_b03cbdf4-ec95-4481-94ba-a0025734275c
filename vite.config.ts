import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    hmr: {
      overlay: true,
      port: 24678, // Use a specific port for HMR
    },
    watch: {
      usePolling: true,
      interval: 100, // Check for changes every 100ms
      ignored: ['**/node_modules/**', '**/.git/**'],
    },
  },
  plugins: [
    react({
      // React SWC plugin automatically includes fast refresh in development
      // No need to explicitly set fastRefresh
    }),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  // Optimize dependencies for better performance
  optimizeDeps: {
    include: ['react', 'react-dom', '@supabase/supabase-js'],
    exclude: ['@vite/client', '@vite/env'],
  },
  // Enable source maps for better debugging
  build: {
    sourcemap: mode === 'development',
  },
  // Clear screen on reload in development
  clearScreen: false,
}));
